# ServiceAbility HCE 集成方案

## 概述

本方案实现了让HCE刷卡服务通过ServiceAbility作为后台服务运行，然后在ServiceAbility中拉起EntryAbility界面的功能。

## 架构设计

```
┌─────────────────┐    HCE事件    ┌─────────────────┐    启动界面    ┌─────────────────┐
│   NFC读卡器     │ ──────────→  │  ServiceAbility │ ──────────→  │   EntryAbility  │
│                 │              │   (后台服务)     │              │    (UI界面)     │
└─────────────────┘              └─────────────────┘              └─────────────────┘
                                          │
                                          ▼
                                 ┌─────────────────┐
                                 │   HceManager    │
                                 │  (HCE事件处理)  │
                                 └─────────────────┘
```

## 主要组件

### 1. ServiceAbility (后台服务)
- **文件**: `entry/src/main/ets/serviceability/ServiceAbility.ets`
- **功能**: 
  - 在后台运行，处理HCE刷卡事件
  - 接收到HCE事件时启动EntryAbility界面
  - 管理HCE服务的生命周期

### 2. HceManager (HCE管理器)
- **文件**: `entry/src/main/ets/models/HceManager.ets`
- **功能**:
  - 处理HCE相关的所有操作
  - 提供专门为ServiceAbility设计的初始化方法
  - 处理APDU命令和响应

### 3. EntryAbility (UI界面)
- **文件**: `entry/src/main/ets/entryability/EntryAbility.ets`
- **功能**:
  - 专注于UI展示
  - 启动时自动启动ServiceAbility后台服务
  - 处理来自ServiceAbility的启动参数

## 配置文件

### module.json5 配置
- **ServiceAbility配置**: 
  - 名称: `PaymentAbility`
  - 类型: `extensionAbilities`
  - 启动模式: `singleton`
  - 支持的Actions: `ohos.nfc.cardemulation.action.HOST_APDU_SERVICE`

### 权限配置
- `ohos.permission.NFC_CARD_EMULATION`: NFC卡模拟权限
- `ohos.permission.START_ABILITIES_FROM_BACKGROUND`: 后台启动Ability权限
- `ohos.permission.KEEP_BACKGROUND_RUNNING`: 后台运行权限

## 工作流程

### 1. 应用启动流程
1. 用户启动应用，EntryAbility被创建
2. EntryAbility在onCreate中启动ServiceAbility后台服务
3. ServiceAbility初始化HCE服务并开始监听HCE事件

### 2. HCE刷卡流程
1. NFC读卡器发送APDU命令
2. ServiceAbility中的HceManager接收到HCE事件
3. HceManager触发回调，ServiceAbility启动EntryAbility
4. EntryAbility显示支付界面，处理用户交互

### 3. 数据传递
- 通过Want参数传递交易ID、卡片数据等信息
- 使用AppStorage进行全局数据共享
- 支持启动来源标识（HCE_SERVICE、MANUAL等）

## 测试功能

### ServiceAbilityTest
- **文件**: `entry/src/main/ets/test/ServiceAbilityTest.ets`
- **功能**: 提供完整的测试套件，验证各个组件的功能

### ServiceTestPage
- **文件**: `entry/src/main/ets/pages/ServiceTestPage.ets`
- **功能**: 可视化测试界面，支持手动触发各种测试

### 测试项目
1. ServiceAbility初始化测试
2. HCE事件处理流程测试
3. EntryAbility启动参数测试
4. HCE状态检查
5. 完整支付流程模拟

## 使用方法

### 1. 访问测试页面
- 在主页面右上角点击设置图标旁的测试按钮
- 选择"ServiceAbility测试"进入测试页面

### 2. 运行测试
- 点击"运行完整测试套件"执行所有测试
- 或者单独运行各个测试项目
- 查看测试结果和日志输出

### 3. 模拟HCE事件
- 点击"模拟HCE刷卡事件"按钮
- 观察是否正确启动EntryAbility界面
- 检查数据传递是否正确

## 关键特性

### 1. 后台服务
- ServiceAbility作为真正的后台服务运行
- 即使应用在后台，也能响应HCE事件
- 支持单例模式，避免重复启动

### 2. 事件驱动
- 基于HCE事件驱动的架构
- 自动响应NFC刷卡操作
- 支持多种APDU命令处理

### 3. 数据安全
- 安全的数据传递机制
- 支持交易ID生成和验证
- 完整的错误处理和日志记录

### 4. 可测试性
- 完整的测试框架
- 可视化测试界面
- 详细的测试报告

## 注意事项

1. **设备兼容性**: 需要设备支持NFC和HCE功能
2. **权限申请**: 确保所有必要权限已正确配置
3. **默认支付应用**: 建议设置为默认支付应用以获得最佳体验
4. **后台运行**: ServiceAbility需要后台运行权限
5. **测试环境**: 在真实设备上测试HCE功能，模拟器可能不支持

## 故障排除

### 常见问题
1. **HCE功能不可用**: 检查设备是否支持NFC和HCE
2. **ServiceAbility启动失败**: 检查权限配置和module.json5配置
3. **EntryAbility启动失败**: 检查Want参数和启动权限
4. **HCE事件无响应**: 检查是否设置为默认支付应用

### 调试方法
1. 查看hilog日志输出
2. 使用测试页面验证各个组件
3. 检查AppStorage中的数据状态
4. 验证HCE服务订阅状态
