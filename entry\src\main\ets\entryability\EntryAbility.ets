import { AbilityConstant, ConfigurationConstant, UIAbility, Want } from '@kit.AbilityKit';
import { BusinessError } from '@kit.BasicServicesKit';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { window } from '@kit.ArkUI';

import { ScriptManager } from '../models/ScriptManager';
import { CardManager } from '../models/CardManager';

const DOMAIN = 0x0000;
const TAG = 'TmsWallet-Entry';

export default class EntryAbility extends UIAbility {
  private scriptManager: ScriptManager = ScriptManager.getInstance();
  private cardManager: CardManager = CardManager.getInstance();

  onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    this.context.getApplicationContext().setColorMode(ConfigurationConstant.ColorMode.COLOR_MODE_NOT_SET);
    hilog.info(DOMAIN, TAG, '%{public}s', 'Ability onCreate');

    // 初始化ScriptManager
    this.scriptManager.initialize(this.context);

    // 检查启动来源
    const launchSource = want.parameters?.['launchSource'] as string;
    if (launchSource === 'HCE_SERVICE') {
      hilog.info(DOMAIN, TAG, 'EntryAbility由ServiceAbility启动，来源：HCE刷卡事件');

      // 获取传递的参数
      const transactionId = want.parameters?.['transactionId'] as string;
      const hceCardData = want.parameters?.['hceCardData'] as string;

      hilog.info(DOMAIN, TAG, '交易ID: %{public}s, 卡片数据: %{public}s', transactionId, hceCardData);

      // 可以在这里处理特定的HCE启动逻辑，比如直接跳转到支付页面
    } else {
      hilog.info(DOMAIN, TAG, 'EntryAbility正常启动');
    }

    // 启动ServiceAbility作为后台服务
    this.startServiceAbility();
  }

  onDestroy(): void {
    hilog.info(DOMAIN, TAG, '%{public}s', 'Ability onDestroy');
    // EntryAbility销毁时不需要停止HCE服务，因为HCE服务由ServiceAbility管理
  }

  onWindowStageCreate(windowStage: window.WindowStage): void {
    // Main window is created, set main page for this ability
    hilog.info(DOMAIN, TAG, '%{public}s', 'Ability onWindowStageCreate');

    // 加载卡片列表
    this.cardManager.loadCardsFromStorage(this.context);

    windowStage.loadContent('pages/MainPage', (err) => {
      if (err.code) {
        hilog.error(DOMAIN, TAG, 'Failed to load the content. Cause: %{public}s', JSON.stringify(err));
        return;
      }
      hilog.info(DOMAIN, TAG, 'Succeeded in loading the content.');
    });
  }

  onWindowStageDestroy(): void {
    // Main window is destroyed, release UI related resources
    hilog.info(DOMAIN, TAG, '%{public}s', 'Ability onWindowStageDestroy');
  }

  onForeground(): void {
    hilog.info(DOMAIN, TAG, '%{public}s', 'Ability onForeground');
  }

  onBackground(): void {
    hilog.info(DOMAIN, TAG, '%{public}s', 'Ability onBackground');
  }

  /**
   * 启动ServiceAbility作为后台服务
   */
  private startServiceAbility(): void {
    try {
      const want: Want = {
        bundleName: this.context.abilityInfo.bundleName,
        abilityName: 'PaymentAbility', // ServiceAbility在module.json5中的名称
        moduleName: this.context.abilityInfo.moduleName || "entry"
      };

      hilog.info(DOMAIN, TAG, '正在启动ServiceAbility后台服务');

      this.context.startAbility(want).then(() => {
        hilog.info(DOMAIN, TAG, 'ServiceAbility后台服务启动成功');
      }).catch((err: BusinessError) => {
        hilog.error(DOMAIN, TAG, '启动ServiceAbility失败: %{public}s', JSON.stringify(err));
      });
    } catch (error) {
      hilog.error(DOMAIN, TAG, '启动ServiceAbility时发生异常: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 处理支付相关操作（保留原有功能，但不再直接处理HCE事件）
   */
  public showPaymentPage(): void {
    try {
      // 生成交易ID
      const transactionId = this.generateTransactionId();

      // 获取当前的HCE卡片数据
      const hceCardData = AppStorage.get('hceCardData') as string || '';

      // 设置AppStorage数据
      AppStorage.setOrCreate('transactionId', transactionId);
      AppStorage.setOrCreate('hceCardData', hceCardData);
      AppStorage.setOrCreate('launchSource', 'MANUAL');

      hilog.info(DOMAIN, TAG, '手动显示支付页面，交易ID: %{public}s', transactionId);

      // 这里可以导航到支付页面或显示支付界面
      // 具体实现取决于您的UI设计

    } catch (error) {
      hilog.error(DOMAIN, TAG, '显示支付页面时发生异常: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 生成交易ID
   */
  private generateTransactionId(): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000);
    return `TXN_${timestamp}_${random}`;
  }



}