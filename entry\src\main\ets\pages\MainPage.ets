import { hilog } from '@kit.PerformanceAnalysisKit';
import { NavigationManager, NavigationUtils } from '../models/NavigationManager';
import { AddCardPageBuilder } from './AddCardSubPage';
import { HceManagerSubPage } from './HceManagerSubPage';
import { CardManager, CardModel } from '../models/CardManager';
import { HceManager } from '../models/HceManager';
import { ScriptManager } from '../models/ScriptManager';
import { CardItem } from '../components/CardItem';
import { VersionInfo } from '../components/VersionInfo';
import { PaymentFlowTest } from '../test/PaymentFlowTest';
import { ServiceTestPage } from './ServiceTestPage';

const DOMAIN = 0x0000;
const TAG = 'TmsWallet-MainPage';

@Entry
@Component
struct MainPage {
  private navigationStack: NavPathStack = new NavPathStack();
  @StorageLink('cardManager') private cardManager: CardManager = CardManager.getInstance();
  private hceManager: HceManager = HceManager.getInstance();
  private scriptManager: ScriptManager = ScriptManager.getInstance();

  aboutToAppear(): void {
    try {
      // 设置全局Navigation栈
      NavigationUtils.setNavigationStack(this.navigationStack);
      // 初始化StorageLink
      AppStorage.setOrCreate('cardManager', this.cardManager);
      hilog.info(DOMAIN, TAG, 'aboutToAppear - 主页面初始化完成');
      if (this.cardManager && this.cardManager.cards) {
        hilog.info(DOMAIN, TAG, '初始卡片数量: %{public}d', this.cardManager.cards.length);
      }
    } catch (error) {
      hilog.error(DOMAIN, TAG, 'aboutToAppear失败: %{public}s', JSON.stringify(error));
    }
  }

  @Builder
  SubTitleBuilder() {
    Column() {
      Text('智能卡')
        .fontSize(20)
        .fontWeight(FontWeight.Medium)
        .fontColor($r('sys.color.font_primary'))
        .textAlign(TextAlign.Center)
        .margin({ bottom: 4 })

      // 添加一个细线分隔
      Divider()
        .width('60%')
        .height(1)
        .color($r('sys.color.ohos_id_color_list_separator'))
        .opacity(0.3)
    }
    .width('100%')
    .padding({ top: 20, bottom: 16 })
    .alignItems(HorizontalAlign.Center)
  }

  // 获取Navigation菜单项
  getNavigationMenus(): NavigationMenuItem[] {
    try {
      hilog.info(DOMAIN, TAG, 'getNavigationMenus被调用');
      const menus: NavigationMenuItem[] = [];

      // 测试按钮
      menus.push({
        value: '',
        icon: $r('sys.media.ohos_ic_public_play'),
        action: () => {
          this.onTestPaymentFlowClick();
        }
      });

      // ServiceAbility测试按钮
      menus.push({
        value: '',
        icon: $r('sys.media.ohos_ic_public_settings'),
        action: () => {
          this.onServiceTestClick();
        }
      });

      // 设置按钮
      menus.push({
        value: '',
        icon: $r('app.media.settings'),
        action: () => {
          this.onSettingsClick();
        }
      });

      hilog.info(DOMAIN, TAG, '返回菜单项数量: %{public}d', menus.length);
      return menus;
    } catch (error) {
      hilog.error(DOMAIN, TAG, 'getNavigationMenus失败: %{public}s', JSON.stringify(error));
      return [];
    }
  }

  private onTestPaymentFlowClick(): void {
    try {
      hilog.info(DOMAIN, TAG, '测试支付流程按钮被点击');

      // 运行支付流程测试
      PaymentFlowTest.runFullTest();

      // 也可以通过HceManager模拟
      this.hceManager.simulatePaymentFlow();

    } catch (error) {
      hilog.error(DOMAIN, TAG, '测试支付流程失败: %{public}s', JSON.stringify(error));
    }
  }

  private onSettingsClick(): void {
    try {
      hilog.info(DOMAIN, TAG, '设置按钮被点击');
      if (this.cardManager) {
        const selectedCard = this.cardManager.cards.find(card => card.isSelected);
        const selectedCardId = selectedCard ? selectedCard.id : '';
        hilog.info(DOMAIN, TAG, '当前选中卡片: %{public}s', selectedCardId || '无');
        this.cardManager.isMenuOpen = true;
      } else {
        hilog.error(DOMAIN, TAG, 'cardManager为空');
      }
    } catch (error) {
      hilog.error(DOMAIN, TAG, '设置按钮点击处理失败: %{public}s', JSON.stringify(error));
    }
  }

  private onServiceTestClick(): void {
    try {
      hilog.info(DOMAIN, TAG, 'ServiceAbility测试按钮被点击');
      NavigationUtils.pushSubPage(NavigationManager.SERVICE_TEST_PAGE);
    } catch (error) {
      hilog.error(DOMAIN, TAG, 'ServiceAbility测试按钮点击处理失败: %{public}s', JSON.stringify(error));
    }
  }



  @Builder
  PaymentAppTipBuilder() {
    if (this.hceManager.hasCapability && !this.hceManager.isDefaultPaymentApp) {
      Row() {
        Image($r('app.media.warning'))
          .width(20)
          .height(20)
          .margin({ left: 16, right: 8 })
          .fillColor($r('sys.color.warning'))

        Text('请在系统设置中将本应用设为默认付款应用')
          .fontSize(14)
          .fontColor($r('sys.color.font_secondary'))
          .layoutWeight(1)
      }
      .width('100%')
      .height(56)
      .backgroundColor($r('sys.color.background_secondary'))
      .margin({ top: 8 })
      .borderRadius(8)
      .padding({ left: 8, right: 16 })
      .alignItems(VerticalAlign.Center)
    }
  }

  @Builder
  CardListBuilder() {
    if (this.cardManager.cards.length === 0) {
      Column() {
        Image($r('app.media.empty_card'))
          .width(120)
          .height(120)
          .opacity(0.6)

        Text('暂无卡片')
          .fontSize(16)
          .fontColor($r('sys.color.font_secondary'))
          .margin({ top: 16 })
      }
      .width('100%')
      .justifyContent(FlexAlign.Center)
      .margin({ top: 60 })
    } else {
      Column() {
        ForEach(this.cardManager.cards, (card: CardModel) => {
          CardItem({
            card: card,
            onSelect: (cardId: string) => {
              this.cardManager.selectCard(cardId);
            },
            onManage: () => {
              this.cardManager.isMenuOpen = true;
            }
          })
            .margin({ bottom: 16 })
            .animation({
              duration: 300,
              curve: Curve.EaseInOut
            })
        }, (card: CardModel) => card.id)
      }
      .width('100%')
      .padding({ left: 20, right: 20, top: 8 })
    }
  }

  @Builder
  MenuBuilder() {
    if (this.cardManager.isMenuOpen) {
      Column({ space: 8 }) {
        Button('删除卡片')
          .width('100%')
          .height(44)
          .backgroundColor($r('sys.color.background_primary'))
          .fontColor(this.cardManager.cards.find(card => card.isSelected) ? $r('sys.color.alert') : $r('sys.color.font_tertiary'))
          .fontSize(16)
          .borderRadius(8)
          .enabled(!!this.cardManager.cards.find(card => card.isSelected))
          .onClick(() => {
            if (this.cardManager.cards.find(card => card.isSelected)) {
              this.cardManager.deleteCard();
            }
          })

        Button('默认支付')
          .width('100%')
          .height(44)
          .backgroundColor($r('sys.color.background_primary'))
          .fontColor($r('sys.color.font_primary'))
          .fontSize(16)
          .borderRadius(8)
          .onClick(async () => {
            this.cardManager.isMenuOpen = false;
            const success = await this.hceManager.requestDefaultPaymentApp();
            if (success) {
              hilog.info(DOMAIN, TAG, '设置默认付款应用成功');
            }
          })

        Button('HCE管理')
          .width('100%')
          .height(44)
          .backgroundColor($r('sys.color.background_primary'))
          .fontColor($r('sys.color.font_primary'))
          .fontSize(16)
          .borderRadius(8)
          .onClick(() => {
            this.cardManager.isMenuOpen = false;
            NavigationUtils.pushPath(NavigationManager.HCE_MANAGER_PAGE);
          })
      }
      .width(140)  // 增加宽度以适应新按钮
      .padding(8)
      .borderRadius(8)
      .backgroundColor($r('sys.color.background_primary'))
      .position({ x: '100%', y: 50 })  // 相对于右上角定位
      .translate({ x: -150, y: 0 })  // 向左偏移，紧贴设置按钮
      .shadow({
        radius: 12,
        color: 'rgba(0, 0, 0, 0.2)',
        offsetX: 0,
        offsetY: 4
      })
      .zIndex(2)
      .animation({
        duration: 200,
        curve: Curve.EaseOut
      })
    }
  }

  @Builder
  MenuOverlayBuilder() {
    if (this.cardManager.isMenuOpen) {
      Column()
        .width('100%')
        .height('100%')
        .backgroundColor('rgba(0, 0, 0, 0.4)')
        .onClick(() => {
          this.cardManager.isMenuOpen = false;
        })
        .zIndex(1)
        .animation({
          duration: 200,
          curve: Curve.EaseOut
        })
    }
  }

  @Builder
  PageMap(name: string) {
    if (name === NavigationManager.ADD_CARD_PAGE) {
      AddCardPageBuilder(this.scriptManager)
    } else if (name === NavigationManager.HCE_MANAGER_PAGE) {
      HceManagerSubPage()
    } else if (name === NavigationManager.SERVICE_TEST_PAGE) {
      ServiceTestPage()
    }
  }

  build() {
    Navigation(this.navigationStack) {
      Stack({ alignContent: Alignment.TopStart }) {
        Stack() {
          Column() {
            this.SubTitleBuilder()
            this.PaymentAppTipBuilder()

            Scroll() {
              Column() {
                this.CardListBuilder()

                VersionInfo()
                  .margin({ top: 40, bottom: 100 })  // 底部留出悬浮按钮的空间
              }
              .width('100%')
              .height('100%')
            }
            .width('100%')
            .layoutWeight(1)
            .scrollBar(BarState.Auto)
            .edgeEffect(EdgeEffect.Spring)
            .nestedScroll({
              scrollForward: NestedScrollMode.PARENT_FIRST,
              scrollBackward: NestedScrollMode.SELF_FIRST
            })
          }
          .width('100%')
          .height('100%')

          // 悬浮胶囊式添加按钮
          Button({ type: ButtonType.Capsule, stateEffect: false }) {
            Row() {
              Image($r('app.media.add'))
                .width(20)
                .height(20)
                .fillColor(Color.White)
                .margin({ right: 8 })

              Text('添加卡片')
                .fontSize(16)
                .fontColor(Color.White)
                .fontWeight(FontWeight.Medium)
            }
            .alignItems(VerticalAlign.Center)
          }
          .height(48)
          .padding({ left: 24, right: 24 })
          .backgroundColor('#007AFF')
          .borderRadius(24)
          .shadow({
            radius: 16,
            color: 'rgba(0, 122, 255, 0.3)',
            offsetX: 0,
            offsetY: 8
          })
          .hoverEffect(HoverEffect.Scale)
          .position({ x: '50%', y: '100%' })
          .translate({ x: '-50%', y: -80 })  // 距离底部80px，水平居中
          .onClick(() => {
            NavigationUtils.pushSubPage(NavigationManager.ADD_CARD_PAGE);
          })
        }
        .width('100%')
        .height('100%')
        .backgroundColor($r('sys.color.ohos_id_color_sub_background'))

        this.MenuBuilder()
        this.MenuOverlayBuilder()
      }
      .width('100%')
      .height('100%')
    }
    .title('卡包')
    .titleMode(NavigationTitleMode.Mini)
    .hideBackButton(true)
    .backgroundColor($r('sys.color.ohos_id_color_background'))
    .menus(this.getNavigationMenus())
    .navDestination(this.PageMap)
    .mode(NavigationMode.Stack)
  }
}