import { hilog } from '@kit.PerformanceAnalysisKit';
import { common } from '@kit.AbilityKit';

const DOMAIN = 0x0000;
const TAG = 'TmsWallet-PaymentShow';

interface TransactionData {
  transactionId: string;
  cardData: string;
  launchSource: string;
}

@Entry
@Component
struct PaymentShowPage {
  @State private transactionData: TransactionData = {
    transactionId: '',
    cardData: '',
    launchSource: ''
  };

  // 监听hceEventTrigger变化来更新界面
  @StorageLink('hceEventTrigger') @Watch('onHceEventChanged') hceEventTrigger: string = '';

  aboutToAppear(): void {
    hilog.info(DOMAIN, TAG, 'PaymentShowPage initialized');
    this.loadTransactionData();

    // 初始化AppStorage
    AppStorage.setOrCreate('hceEventTrigger', '');

    // 如果没有交易数据，延迟检查一次（处理异步加载情况）
    if (!this.transactionData.transactionId) {
      setTimeout(() => {
        this.loadTransactionData();
      }, 200);
    }
  }

  /**
   * 页面即将显示时调用
   */
  onPageShow(): void {
    // 页面显示时重新检查交易数据
    this.loadTransactionData();
  }

  /**
   * 监听hceEventTrigger变化
   */
  onHceEventChanged(): void {
    if (this.hceEventTrigger) {
      hilog.info(DOMAIN, TAG, 'HCE event triggered, reloading transaction data');
      this.loadTransactionData();
    }
  }

  /**
   * 加载交易数据
   */
  private loadTransactionData(): void {
    const previousTransactionId = this.transactionData.transactionId;

    this.transactionData = {
      transactionId: AppStorage.get('transactionId') as string || '',
      cardData: AppStorage.get('hceCardData') as string || '',
      launchSource: AppStorage.get('launchSource') as string || ''
    };

    // 如果交易ID发生变化，记录日志
    if (previousTransactionId !== this.transactionData.transactionId) {
      hilog.info(DOMAIN, TAG, 'Transaction data updated: %{public}s -> %{public}s',
        previousTransactionId, this.transactionData.transactionId);
    }
  }

  /**
   * 处理支付操作
   */
  private handlePayment(confirmed: boolean): void {
    const transactionId = this.transactionData.transactionId;
    const action = confirmed ? '确认支付' : '取消支付';

    hilog.info(DOMAIN, TAG, `${action}，交易ID: ${transactionId}`);

    if (confirmed) {
      this.processPayment();
    }

    // 直接关闭PaymentAbility
    this.closePaymentAbility();
  }

  /**
   * 执行支付逻辑
   */
  private processPayment(): void {
    // TODO: 实现具体的支付逻辑
    hilog.info(DOMAIN, TAG, '执行支付逻辑');
  }

  /**
   * 关闭PaymentAbility
   */
  private closePaymentAbility(): void {
    try {
      const context = getContext(this) as common.UIAbilityContext;
      context.terminateSelf();
      hilog.info(DOMAIN, TAG, 'PaymentAbility closed');
    } catch (error) {
      hilog.error(DOMAIN, TAG, 'Failed to close PaymentAbility: %{public}s', String(error));
    }
  }

  build() {
    Column({ space: 20 }) {
      this.HeaderSection()
      this.TransactionInfoSection()
      Blank()
      this.ActionButtonsSection()
    }
    .width('100%')
    .height('100%')
    .padding(20)
    .justifyContent(FlexAlign.Start)
    .alignItems(HorizontalAlign.Center)
  }

  /**
   * 页面标题组件
   */
  @Builder
  HeaderSection() {
    Text('支付确认')
      .fontSize(24)
      .fontWeight(FontWeight.Bold)
  }

  /**
   * 交易信息展示组件
   */
  @Builder
  TransactionInfoSection() {
    if (this.transactionData.transactionId) {
      Column({ space: 8 }) {
        Text('交易信息')
          .fontSize(18)
          .fontWeight(FontWeight.Medium)
          .alignSelf(ItemAlign.Start)

        this.InfoItem('交易ID', this.transactionData.transactionId)
        this.InfoItem('启动源', this.transactionData.launchSource)

        if (this.transactionData.cardData) {
          this.InfoItem('卡片数据', `${this.transactionData.cardData.substring(0, 20)}...`)
        }
      }
      .alignItems(HorizontalAlign.Start)
      .width('100%')
      .padding(20)
      .backgroundColor('#f5f5f5')
      .borderRadius(10)
    }
  }

  /**
   * 信息项组件
   */
  @Builder
  InfoItem(label: string, value: string) {
    Text(`${label}: ${value}`)
      .fontSize(16)
      .alignSelf(ItemAlign.Start)
  }

  /**
   * 操作按钮组件
   */
  @Builder
  ActionButtonsSection() {
    Row({ space: 20 }) {
      this.ActionButton('取消', '#ff6b6b', () => this.handlePayment(false))
      this.ActionButton('确认支付', '#51cf66', () => this.handlePayment(true))
    }
    .justifyContent(FlexAlign.SpaceEvenly)
    .width('100%')
  }

  /**
   * 通用按钮组件
   */
  @Builder
  ActionButton(text: string, bgColor: string, onClick: () => void) {
    Button(text)
      .width('40%')
      .height(50)
      .backgroundColor(bgColor)
      .onClick(onClick)
  }


}