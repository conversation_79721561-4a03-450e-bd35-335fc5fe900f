import { hilog } from '@kit.PerformanceAnalysisKit';
import { ServiceAbilityTest } from '../test/ServiceAbilityTest';
import { HceManager } from '../models/HceManager';

const DOMAIN = 0x0000;
const TAG = 'TmsWallet-ServiceTestPage';

@Entry
@Component
struct ServiceTestPage {
  @State testResults: string[] = [];
  @State isTestRunning: boolean = false;
  private serviceTest: ServiceAbilityTest = ServiceAbilityTest.getInstance();
  private hceManager: HceManager = HceManager.getInstance();

  build() {
    Column() {
      // 标题
      Text('ServiceAbility & HCE 测试')
        .fontSize(24)
        .fontWeight(FontWeight.Bold)
        .margin({ top: 20, bottom: 20 })

      // 测试按钮区域
      Column({ space: 15 }) {
        Button('运行完整测试套件')
          .width('90%')
          .height(50)
          .enabled(!this.isTestRunning)
          .onClick(() => {
            this.runCompleteTestSuite();
          })

        Button('测试ServiceAbility初始化')
          .width('90%')
          .height(50)
          .enabled(!this.isTestRunning)
          .onClick(() => {
            this.testServiceInitialization();
          })

        Button('模拟HCE刷卡事件')
          .width('90%')
          .height(50)
          .enabled(!this.isTestRunning)
          .onClick(() => {
            this.simulateHceEvent();
          })

        Button('测试EntryAbility启动')
          .width('90%')
          .height(50)
          .enabled(!this.isTestRunning)
          .onClick(() => {
            this.testEntryAbilityLaunch();
          })

        Button('检查HCE状态')
          .width('90%')
          .height(50)
          .onClick(() => {
            this.checkHceStatus();
          })

        Button('清除测试结果')
          .width('90%')
          .height(50)
          .backgroundColor(Color.Orange)
          .onClick(() => {
            this.clearTestResults();
          })
      }
      .margin({ top: 20, bottom: 20 })

      // 测试结果显示区域
      Text('测试结果:')
        .fontSize(18)
        .fontWeight(FontWeight.Medium)
        .alignSelf(ItemAlign.Start)
        .margin({ left: 20, top: 20, bottom: 10 })

      Scroll() {
        Column() {
          ForEach(this.testResults, (result: string, index: number) => {
            Text(result)
              .fontSize(14)
              .fontColor(result.includes('✅') ? Color.Green : 
                        result.includes('❌') ? Color.Red : 
                        result.includes('⚠️') ? Color.Orange : Color.Black)
              .textAlign(TextAlign.Start)
              .width('100%')
              .padding({ left: 10, right: 10, top: 5, bottom: 5 })
              .backgroundColor(index % 2 === 0 ? '#f5f5f5' : Color.White)
          })
        }
      }
      .width('95%')
      .height(300)
      .border({ width: 1, color: Color.Gray })
      .borderRadius(8)

      // 状态指示器
      if (this.isTestRunning) {
        Row() {
          LoadingProgress()
            .width(30)
            .height(30)
          Text('测试运行中...')
            .fontSize(16)
            .margin({ left: 10 })
        }
        .margin({ top: 20 })
      }
    }
    .width('100%')
    .height('100%')
    .padding(20)
  }

  /**
   * 运行完整测试套件
   */
  private runCompleteTestSuite(): void {
    this.isTestRunning = true;
    this.addTestResult('🚀 开始运行完整测试套件...');

    setTimeout(() => {
      try {
        const result = this.serviceTest.runAllTests();
        if (result) {
          this.addTestResult('✅ 完整测试套件通过');
        } else {
          this.addTestResult('❌ 完整测试套件失败');
        }
      } catch (error) {
        this.addTestResult('❌ 测试套件异常: ' + JSON.stringify(error));
      }
      this.isTestRunning = false;
    }, 100);
  }

  /**
   * 测试ServiceAbility初始化
   */
  private testServiceInitialization(): void {
    this.addTestResult('🔧 测试ServiceAbility初始化...');
    
    try {
      const result = this.serviceTest.testServiceAbilityInitialization();
      if (result) {
        this.addTestResult('✅ ServiceAbility初始化测试通过');
      } else {
        this.addTestResult('❌ ServiceAbility初始化测试失败');
      }
    } catch (error) {
      this.addTestResult('❌ ServiceAbility初始化测试异常: ' + JSON.stringify(error));
    }
  }

  /**
   * 模拟HCE刷卡事件
   */
  private simulateHceEvent(): void {
    this.addTestResult('💳 模拟HCE刷卡事件...');
    
    try {
      this.hceManager.simulatePaymentFlow();
      this.addTestResult('✅ HCE刷卡事件模拟完成');
    } catch (error) {
      this.addTestResult('❌ HCE刷卡事件模拟失败: ' + JSON.stringify(error));
    }
  }

  /**
   * 测试EntryAbility启动
   */
  private testEntryAbilityLaunch(): void {
    this.addTestResult('🚀 测试EntryAbility启动参数...');
    
    try {
      const result = this.serviceTest.testEntryAbilityLaunchParameters();
      if (result) {
        this.addTestResult('✅ EntryAbility启动参数测试通过');
      } else {
        this.addTestResult('❌ EntryAbility启动参数测试失败');
      }
    } catch (error) {
      this.addTestResult('❌ EntryAbility启动测试异常: ' + JSON.stringify(error));
    }
  }

  /**
   * 检查HCE状态
   */
  private checkHceStatus(): void {
    this.addTestResult('📊 检查HCE状态...');
    
    try {
      this.hceManager.checkHceCapability();
      this.hceManager.checkDefaultPaymentApp();
      
      this.addTestResult(`HCE能力支持: ${this.hceManager.hasCapability ? '✅' : '❌'}`);
      this.addTestResult(`默认支付应用: ${this.hceManager.isDefaultPaymentApp ? '✅' : '❌'}`);
      this.addTestResult(`HCE服务订阅: ${this.hceManager.isSubscribe ? '✅' : '❌'}`);
    } catch (error) {
      this.addTestResult('❌ HCE状态检查异常: ' + JSON.stringify(error));
    }
  }

  /**
   * 添加测试结果
   */
  private addTestResult(result: string): void {
    const timestamp = new Date().toLocaleTimeString();
    this.testResults.push(`[${timestamp}] ${result}`);
    
    // 限制结果数量，避免内存过多占用
    if (this.testResults.length > 50) {
      this.testResults.splice(0, 10);
    }
    
    hilog.info(DOMAIN, TAG, result);
  }

  /**
   * 清除测试结果
   */
  private clearTestResults(): void {
    this.testResults = [];
    this.addTestResult('🧹 测试结果已清除');
  }
}
