import { UIAbility, Want, AbilityConstant } from '@kit.AbilityKit';
import { BusinessError } from '@kit.BasicServicesKit';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { HceManager } from '../models/HceManager';

const DOMAIN = 0x0000;
const TAG = 'TmsWallet-Service';

/**
 * ServiceAbility - HCE后台服务
 * 负责在后台运行，处理HCE刷卡事件，并在需要时启动EntryAbility界面
 */
export default class ServiceAbility extends UIAbility {
  private hceManager: HceManager = HceManager.getInstance();
  private isServiceRunning: boolean = false;

  onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void {
    hilog.info(DOMAIN, TAG, 'ServiceAbility onCreate');

    try {
      // 使用专门为ServiceAbility设计的初始化方法
      if (!this.hceManager.initializeForService(want, () => this.launchEntryAbility())) {
        hilog.error(DOMAIN, TAG, 'HCE功能初始化失败');
        return;
      }

      // 启动HCE服务
      this.startHceService();
      this.isServiceRunning = true;

      hilog.info(DOMAIN, TAG, 'ServiceAbility HCE后台服务启动成功');
    } catch (error) {
      hilog.error(DOMAIN, TAG, 'ServiceAbility启动失败: %{public}s', JSON.stringify(error));
    }
  }

  onDestroy(): void {
    hilog.info(DOMAIN, TAG, 'ServiceAbility onDestroy');

    try {
      // 停止HCE服务
      this.stopHceService();
      this.isServiceRunning = false;

      hilog.info(DOMAIN, TAG, 'ServiceAbility HCE后台服务已停止');
    } catch (error) {
      hilog.error(DOMAIN, TAG, 'ServiceAbility停止失败: %{public}s', JSON.stringify(error));
    }
  }

  onForeground(): void {
    hilog.info(DOMAIN, TAG, 'ServiceAbility onForeground');
  }

  onBackground(): void {
    hilog.info(DOMAIN, TAG, 'ServiceAbility onBackground');
  }

  /**
   * 启动HCE服务
   */
  private startHceService(): void {
    try {
      // 检查设备是否支持NFC能力和HCE能力
      if (!canIUse("SystemCapability.Communication.NFC.Core")) {
        hilog.error(DOMAIN, TAG, 'NFC功能不可用');
        return;
      }

      // 订阅HCE事件
      this.hceManager.subscribeHce();
      if (!this.hceManager.isSubscribe) {
        hilog.error(DOMAIN, TAG, '启动HCE后台刷卡服务失败');
        return;
      }

      hilog.info(DOMAIN, TAG, 'HCE后台服务启动成功');
    } catch (error) {
      hilog.error(DOMAIN, TAG, '启动HCE服务失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 停止HCE服务
   */
  private stopHceService(): void {
    try {
      this.hceManager.unsubscribeHce();
      hilog.info(DOMAIN, TAG, 'HCE服务已停止');
    } catch (error) {
      hilog.error(DOMAIN, TAG, '停止HCE服务失败: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 启动EntryAbility界面
   * 当接收到HCE刷卡事件时调用
   */
  private launchEntryAbility(): void {
    try {
      // 生成交易ID
      const transactionId = this.generateTransactionId();

      // 获取当前的HCE卡片数据
      const hceCardData = AppStorage.get('hceCardData') as string || '';

      // 设置AppStorage数据供EntryAbility使用
      AppStorage.setOrCreate('transactionId', transactionId);
      AppStorage.setOrCreate('hceCardData', hceCardData);
      AppStorage.setOrCreate('launchSource', 'HCE_SERVICE');

      const want: Want = {
        bundleName: this.context.abilityInfo.bundleName,
        abilityName: 'EntryAbility',
        moduleName: this.context.abilityInfo.moduleName || "entry",
        parameters: {
          transactionId: transactionId,
          launchSource: 'HCE_SERVICE',
          hceCardData: hceCardData
        }
      };

      hilog.info(DOMAIN, TAG, '正在从ServiceAbility启动EntryAbility，交易ID: %{public}s', transactionId);

      this.context.startAbility(want).then(() => {
        hilog.info(DOMAIN, TAG, 'EntryAbility启动成功');
      }).catch((err: BusinessError) => {
        hilog.error(DOMAIN, TAG, '启动EntryAbility失败: %{public}s', JSON.stringify(err));
      });
    } catch (error) {
      hilog.error(DOMAIN, TAG, '启动EntryAbility时发生异常: %{public}s', JSON.stringify(error));
    }
  }

  /**
   * 生成交易ID
   */
  private generateTransactionId(): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000);
    return `SVC_TXN_${timestamp}_${random}`;
  }

  /**
   * 获取服务运行状态
   */
  public getServiceStatus(): boolean {
    return this.isServiceRunning;
  }

  /**
   * 测试方法：模拟HCE事件触发
   */
  public simulateHceEvent(): void {
    hilog.info(DOMAIN, TAG, '模拟HCE事件触发');
    this.launchEntryAbility();
  }
}